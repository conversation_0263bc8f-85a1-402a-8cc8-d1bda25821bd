import base64
import os
import random
import time
import logging
import sys
from datetime import datetime
import uuid
import re
from flask import Flask, render_template, request, jsonify, send_from_directory, send_file
#import google.generativeai as genai
from google import genai
from config import API_KEYS, GEMINI_FLASH_MODEL
import io
from generate_speech_table import generate_speech_table, generate_srt_files_and_table
try:
    from docx import Document
    from docx.shared import Inches
except ImportError:
    Document = None  # Will error if not installed
from bs4 import BeautifulSoup

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] [%(name)s] %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    handlers=[
        logging.FileHandler('app.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

# Create specific loggers for different components
speech_logger = logging.getLogger('speech_table')
srt_logger = logging.getLogger('srt_files')
api_logger = logging.getLogger('api_operations')

# Keep track of used API keys for cycling
current_key_index = 0
INVALID_API_KEYS = set()

# Create Flask app
app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB file size limit
app.config['UPLOAD_FOLDER'] = 'temp'

# Ensure temp directory exists
temp_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "temp")
if not os.path.exists(temp_dir):
    os.makedirs(temp_dir)
    logging.info(f"Created temp directory: {temp_dir}")

def get_next_api_key():
    """Get the next API key using cycling strategy"""
    global current_key_index
    valid_keys = [key for key in API_KEYS if key not in INVALID_API_KEYS]
    print(f"[步骤] 当前有效API KEY数量: {len(valid_keys)}，已失效KEY数量: {len(INVALID_API_KEYS)}")
    if not valid_keys:
        logging.error("No valid API keys available")
        print("[错误] 没有可用的API KEY！")
        raise ValueError("No valid API keys available")
    # Select initial key randomly if it's the first use
    if current_key_index == 0:
        current_key_index = random.randint(0, len(valid_keys) - 1)
        api_logger.info(f"首次使用，随机选择API KEY索引: {current_key_index}")
        print(f"[步骤] 首次使用，随机选择KEY索引: {current_key_index}")
    else:
        # Move to next key
        current_key_index = (current_key_index + 1) % len(valid_keys)
        api_logger.info(f"轮换到下一个API KEY索引: {current_key_index}")
        print(f"[步骤] 轮换到下一个KEY索引: {current_key_index}")
    selected_key = valid_keys[current_key_index]
    api_logger.info(f"当前使用API KEY前10位: {selected_key[:10]}")
    print(f"[信息] 当前使用API KEY前10位: {selected_key[:10]}")
    return selected_key

def save_binary_file(file_name, data):
    """Save binary data to a file"""
    print(f"[步骤] 保存二进制文件: {file_name}")
    try:
        with open(file_name, "wb") as f:
            f.write(data)
        print(f"[成功] 文件保存成功: {file_name}")
        return True
    except Exception as e:
        logging.error(f"Error saving file {file_name}: {e}")
        print(f"[错误] 文件保存失败: {file_name}, 错误: {e}")
        return False

def beautify_text(text):
    """美化文本，去除多余换行，对章节进行合理分段，保持文本连贯性

    根据用户需求优化文本排版，确保特殊标记（如标题、场景描述等）保持在同一行，
    减少不必要的换行，使文本更加连贯易读。同时保留HTML标签。

    Args:
        text (str): 需要美化的原始文本

    Returns:
        str: 美化后的文本
    """
    if not text or not isinstance(text, str):
        return text

    # 保存HTML标签
    html_tags = []
    def save_html_tag(match):
        html_tags.append(match.group(0))
        return f"__HTML_TAG_{len(html_tags)-1}__"

    # 临时保存HTML标签
    text_with_placeholders = re.sub(r'<[^>]+>', save_html_tag, text)

    # 去除多余换行，保留段落分隔
    lines = text_with_placeholders.split('\n')
    processed_lines = []
    current_paragraph = []

    # 识别章节标记和特殊标题
    section_markers = [
        '【生图提示词】', '【旁白', '【旁白配音】', '【',
        '生图提示词：', '旁白：', '旁白配音：',
        '主题：', '故事：', '对白：', '场景配图生图prompt:',
        '第一页', '第二页', '第三页', '第四页', '第五页'
    ]

    # 标题和内容标记，这些标记后的内容应该与标记保持在同一行
    title_markers = ['主题：', '故事：', '对白：', '场景配图生图prompt:']

    for i, line in enumerate(lines):
        line = line.strip()
        if not line:
            # 空行作为段落分隔符
            if current_paragraph:
                processed_lines.append(' '.join(current_paragraph))
                current_paragraph = []
        elif any(marker in line for marker in section_markers):
            # 章节标记作为新段落的开始
            if current_paragraph:
                processed_lines.append(' '.join(current_paragraph))

            # 检查是否是标题标记，如果是，尝试将下一行内容合并到当前行
            if any(marker in line for marker in title_markers) and i + 1 < len(lines):
                next_line = lines[i + 1].strip()
                if next_line and not any(marker in next_line for marker in section_markers):
                    # 将标题和内容合并为一行
                    current_paragraph = [line + ' ' + next_line]
                    # 跳过下一行，因为已经处理过了
                    lines[i + 1] = ''
                else:
                    current_paragraph = [line]
            else:
                current_paragraph = [line]
        else:
            # 如果当前行不是空行且不包含章节标记，则添加到当前段落
            if current_paragraph and not any(marker in current_paragraph[0] for marker in title_markers):
                current_paragraph.append(line)
            else:
                # 如果当前没有段落或当前段落是标题，则创建新段落
                if current_paragraph:
                    processed_lines.append(' '.join(current_paragraph))
                current_paragraph = [line]

    # 添加最后一个段落
    if current_paragraph:
        processed_lines.append(' '.join(current_paragraph))

    # 合并处理后的段落，使用HTML段落标签
    result = '<p>' + '</p><p>'.join(processed_lines) + '</p>'

    # 恢复HTML标签
    for i, tag in enumerate(html_tags):
        result = result.replace(f"__HTML_TAG_{i}__", tag)

    return result

def convert_text_to_html(raw_text: str) -> str:
    """
    将LLM返回的纯文本转换为简单的HTML格式用于显示。
    保持原始文本结构，只添加基本的HTML标签。
    """
    if not raw_text:
        return ""

    # 去除代码块包裹标记（如果有的话）
    cleaned = raw_text.strip()
    if cleaned.startswith('```'):
        lines = cleaned.split('\n')
        if lines[0].startswith('```'):
            lines = lines[1:]
        if lines and lines[-1].strip() == '```':
            lines = lines[:-1]
        cleaned = '\n'.join(lines)

    # 按行分割文本
    lines = cleaned.split('\n')
    html_lines = []

    for line in lines:
        line = line.strip()
        if not line:
            # 空行保持为空行
            html_lines.append('')
        elif line.startswith('标题：'):
            # 标题处理
            title = line.replace('标题：', '').strip()
            html_lines.append(f'<h1>{title}</h1>')
        elif line.startswith('第') and ('页' in line):
            # 页数标题
            html_lines.append(f'<h2>{line}</h2>')
        elif ':' in line or '：' in line:
            # 包含冒号的行，通常是角色对白或描述
            html_lines.append(f'<p><strong>{line}</strong></p>')
        else:
            # 普通段落
            html_lines.append(f'<p>{line}</p>')

    return '\n'.join(html_lines)

def clean_llm_html(raw_text: str) -> str:
    """
    清理LLM返回的HTML内容，去除```html、```等包裹，确保返回基本HTML结构。
    优化策略：移除所有CSS类和样式属性，只保留基本HTML标签。
    """
    if not raw_text:
        return ""

    # 去除代码块包裹标记
    cleaned = raw_text.strip()
    if cleaned.startswith('```html'):
        cleaned = cleaned[len('```html'):].strip()
    elif cleaned.startswith('```'):
        cleaned = cleaned[len('```'):].strip()
    if cleaned.endswith('```'):
        cleaned = cleaned[:-3].strip()

    # 清理HTML，移除所有CSS类和样式属性
    try:
        soup = BeautifulSoup(cleaned, "html.parser")

        # 移除所有标签的class和style属性
        for tag in soup.find_all():
            if tag.has_attr('class'):
                del tag['class']
            if tag.has_attr('style'):
                del tag['style']
            # 保留基本的HTML属性，如href, src等
            # 但移除所有样式相关的属性
            attrs_to_remove = []
            for attr in tag.attrs:
                if attr in ['class', 'style', 'id']:
                    attrs_to_remove.append(attr)
            for attr in attrs_to_remove:
                del tag[attr]

        # 如果既没有html也没有body标签，直接返回清理后的内容
        if not soup.find('html') and not soup.find('body'):
            return str(soup)

        return str(soup)
    except Exception as e:
        # 解析失败时返回原始内容，确保不丢失信息
        api_logger.warning(f"HTML解析失败，返回原始内容: {str(e)}")
        return cleaned

def apply_tailwind_styles(html: str) -> str:
    """
    为 LLM 返回的 HTML 添加轻量化的 Tailwind CSS 样式
    优化策略：保持原始结构，仅添加必要的美化样式
    """
    try:
        soup = BeautifulSoup(html, "html.parser")

        # 轻量化样式应用，不改变原有结构
        # 标题美化
        for h in soup.find_all(['h1', 'h2', 'h3']):
            existing_class = h.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            h['class'] = existing_class + ["font-bold", "text-2xl", "text-pink-600", "mb-4"]

        # 段落美化
        for p in soup.find_all('p'):
            existing_class = p.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            p['class'] = existing_class + ["text-lg", "leading-relaxed", "text-gray-800", "mb-2"]

        # 列表美化
        for ul in soup.find_all('ul'):
            existing_class = ul.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            ul['class'] = existing_class + ["list-disc", "pl-6", "text-base", "text-blue-700"]

        for li in soup.find_all('li'):
            existing_class = li.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            li['class'] = existing_class + ["mb-1"]

        # 表格美化
        for table in soup.find_all('table'):
            existing_class = table.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            table['class'] = existing_class + ["min-w-full", "bg-white", "border", "border-gray-300", "rounded-lg", "shadow"]

        for th in soup.find_all('th'):
            existing_class = th.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            th['class'] = existing_class + ["bg-blue-200", "text-blue-900", "px-4", "py-2"]

        for td in soup.find_all('td'):
            existing_class = td.get('class', [])
            if isinstance(existing_class, str):
                existing_class = existing_class.split()
            td['class'] = existing_class + ["border", "px-4", "py-2"]

        return str(soup)
    except Exception as e:
        api_logger.warning(f"Tailwind样式应用失败，返回原始HTML: {str(e)}")
        return html

def generate_with_key(
    prompt: str,
    api_key: str,
    model: str = GEMINI_FLASH_MODEL,
    max_retries: int = 3
) -> dict:
    """
    使用指定API key生成内容，支持重试机制
    """
    retry_count = 0
    error_msg = "未知错误"
    import traceback
    while retry_count < max_retries:
        try:
            api_logger.info(f"开始第{retry_count+1}次生成尝试")
            print(f"[步骤] 第{retry_count+1}次尝试，使用API KEY前10位: {api_key[:10]}")
            client = genai.Client(api_key=api_key)
            # 先发送初始化提示词，清空上下文
            init_prompt = "请开始一个新的话题，忘掉之前的资料。你明白就回答OK"
            print("[步骤] 发送初始化提示词，清空上下文...")
            api_logger.info("发送初始化提示词，清空上下文...")
            init_response = client.models.generate_content(
                model=model,
                contents=init_prompt)
            init_text = getattr(init_response, "text", "")
            api_logger.debug(f"初始化回复: {init_text}")
            print(f"[DEBUG] 初始化回复: {init_text}")
            # 再发送正式用户prompt
            prefix = "请根据中文排版规范，合理美化分段，并用纯文本格式输出以下完整内容。不要使用任何HTML标签，只使用换行符来分段。每个不同的内容段落之间用一个空行分隔："
            merged_prompt = prefix + "\n" + prompt

            api_logger.info(f"使用模型: {model}")
            api_logger.debug(f"发送Prompt (部分): {merged_prompt[:500]}...") # 避免日志过长
            print("[步骤] 开始调用Gemini模型API...")
            response = client.models.generate_content(
                model=model,
                contents=merged_prompt
            )

            int(f"[DEBUG] Google AI Response: {response}")

            api_logger.info("Gemini模型API调用完成")
            print("[成功] Gemini模型API调用完成，开始处理返回内容...")

            full_text = getattr(response, "text", "")
            api_logger.debug(f"变量 full_text 类型: {type(full_text)}")
            api_logger.debug(f"变量 full_text 内容: '{full_text}'") # 打印实际内容，即使是空的也容易看出来

            api_logger.debug(f"LLM返回原始文本 (部分): {full_text[:500]}...") # 避免日志过长
            api_logger.debug(f"LLM返回原始文本长度: {len(full_text)} 字符")

            results = []
            if full_text:
                # 将纯文本转换为简单的HTML格式用于显示
                formatted_html = convert_text_to_html(full_text)
                api_logger.debug(f"处理后HTML内容 (部分): {formatted_html[:500]}...")
                api_logger.debug(f"处理后HTML内容长度: {len(formatted_html)} 字符")
                results.append({
                    "type": "text",
                    "content": formatted_html,  # 转换后的HTML用于显示
                    "raw_content": full_text,  # 保存原始纯文本内容用于后续处理
                    "display_content_once": True
                })
            else:
                 api_logger.warning("LLM返回原始文本为空")

            # TODO: 如有图片内容，按需处理response结构

            api_logger.info("内容处理完成，返回结果")
            print("[步骤] 内容处理完成，返回结果")
            return {
                "status": "success",
                "results": results,
                "api_key": f"{api_key[:10]}..."
            }
        except Exception as e:
            retry_count += 1
            error_msg = str(e)
            tb_str = traceback.format_exc()
            logging.error(f"Gemini API调用异常: {error_msg}\nTraceback:\n{tb_str}")
            print(f"[错误] Gemini API调用异常: {error_msg}，重试次数: {retry_count}")
            if "API key not valid" in error_msg:
                INVALID_API_KEYS.add(api_key)
                api_logger.error(f"API KEY标记为无效: {api_key[:10]}")
                break # API key invalid, no point in retrying with the same key
            elif retry_count < max_retries:
                api_logger.warning(f"API调用失败，进行第{retry_count+1}次重试...")
                time.sleep(1)
            else:
                api_logger.error(f"API调用失败，已达最大重试次数 {max_retries}")

    api_logger.error(f"所有尝试失败，共重试{retry_count}次，错误信息: {error_msg}")
    print(f"[失败] 所有尝试失败，共重试{retry_count}次，错误信息: {error_msg}")
    return {
        "status": "error",
        "message": f"Failed after {retry_count} attempts: {error_msg}",
        "error_details": tb_str if 'tb_str' in locals() else '',
        "api_key": f"{api_key[:10]}..."
    }

@app.route('/')
def index():
    """Render main page"""
    api_key = get_next_api_key()
    default_prompt = """
    1、请开始一个新的话题，忘掉之前的资料。
    请参考市面上流行的热门童话分类，选择{随机1-2个角色}+{随机全球冒险地点/随机纪念日/随机名人故事}冒险题材的，
    剧情曲折反复，让人意想不到的，再按安徒生和格林童话写作风格，叙事手法等，构思一个儿童绘本故事，
    符合3~8岁儿童特点和认知，300字左右，共4页。每次选定一个主题展开故事（中心思想），配上一个吸引人的标题。
    返回的消息模版如下，不要遗漏任何内容：
    标题：《XXX》
    第X页：
    主持人角色对白：页面内容概述。
    主持人角色配音要求：全篇统一为某个音色，其特色为：成熟稳重、有说服力。
    XXXXX角色对白：XX。
    XXXXX角色配音要求：配音年龄，男女声，配音特色要求。
    注意：以上的XXX角色对白和XXX角色配音要求成对出现。
    每页包含场景配图，生图Prompt详细描述： （
    第一，参考stable diffusion用中文表述，描述需要包含角色和它的名字，动作，表情，以及周边详细环境，镜头，氛围等。
    第二，参考格式如下： 【图片风格为「3D渲染」，色彩鲜艳，七彩气泡，松鼠系着皮质围裙，
    在空心树干内整理刻满年轮的木质档案，前爪举着橡果放大镜，树洞外飘落银杏叶日历，中景，特写，超精细，大师构图，
    背景虚幻，比例 "16:9"】。
    第三，其中注意，图片风格为「3D渲染」，色彩鲜艳，生动活泼，为每个生图prompt必须有的关键字，
    一开始就要加入主体，中景，全景，特写，微距，鱼眼，俯视，仰视等，按情况添加到主体关键字。
    第四，其中注意，专业光影效果，超精细，大师构图，背景虚幻，比例 "16:9"。为每个生图prompt必须有的关键字。）
    这是输出的样例：
    第4页：
    主持人角色对白：XXXXX
    主持人角色配音要求：全篇统一为某个音色，其特色为：成熟稳重、有说服力。
    小白角色对白：XXXXX。
    小白角色配音要求：年轻男孩，好奇，声音轻快。
    吱吱角色对白：XXXX。
    吱吱角色配音要求：年轻女孩，活泼，声音带点机灵。
    主持人角色对白：XXXX。
    主持人角色配音要求：全篇统一为某个音色，其特色为：成熟稳重、有说服力。
    生图Prompt详细描述：
    【图片风格为「3D渲染」，XXXXX，比例 "16:9"】
    2、请直接输出返回内容，不要任何回复。
"""
    logging.info("Rendering index page")
    return render_template('index.html', api_key=f"{api_key[:10]}...", default_prompt=default_prompt, gemini_flash_model=GEMINI_FLASH_MODEL)

@app.route('/generate', methods=['POST'])
def api_generate():
    """API endpoint for generation"""
    try:
        data = request.json
        if not data:
            return jsonify({'status': 'error', 'message': 'No data provided'})

        prompt = data.get('prompt', '')
        if not prompt:
            return jsonify({'status': 'error', 'message': 'No prompt provided'})

        model = data.get('model', GEMINI_FLASH_MODEL)  # 默认模型

        max_keys_to_try = len(API_KEYS) - len(INVALID_API_KEYS)
        keys_tried = 0

        while keys_tried < max_keys_to_try:
            try:
                api_key = get_next_api_key()
                result = generate_with_key(prompt, api_key, model)

                if result["status"] == "success":
                    return jsonify(result)

                # If we got an error but not due to invalid key, return the error
                if "API key not valid" not in result.get("message", ""):
                    return jsonify(result)

                keys_tried += 1

            except Exception as e:
                error_msg = str(e)
                return jsonify({
                    'status': 'error',
                    'message': f'Generation failed: {error_msg}'
                })

        return jsonify({
            'status': 'error',
            'message': f'All available keys failed ({keys_tried} keys tried)'
        })
    except Exception as e:
        error_msg = str(e)
        return jsonify({
            'status': 'error',
            'message': f'Unexpected error: {error_msg}'
        })

@app.route('/test_api', methods=['GET'])
def test_api():
    """Test API connection (简化版，快速返回LLM响应文本)"""
    try:
        # 固定模型为gemini-2.5-flash-preview-04-17
        model = GEMINI_FLASH_MODEL
        api_key = get_next_api_key()
        if not api_key:
            return jsonify({
                'status': 'error',
                'message': 'No valid API key available'
            })
        client = genai.Client(api_key=api_key)
        prompt = "API连接测试：请回复OK"
        try:
            response = client.models.generate_content(
                model=model,
                contents=[{"role": "user", "parts": [{"text": prompt}]}]
            )
            test_text = getattr(response, "text", "")
        except Exception as e:
            test_text = str(e)
        return jsonify({
            'status': 'success',
            'test_text': test_text
        })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'API test failed: {str(e)}'
        })

@app.route('/temp/<filename>')
def serve_file(filename):
    """Serve temporary files"""
    return send_from_directory(temp_dir, filename)

@app.route('/export', methods=['POST'])
def export_story():
    """Export the story to a docx file and return for download"""
    if Document is None:
        return jsonify({'status': 'error', 'message': '服务器未安装python-docx库，请先安装 python-docx'}), 500
    data = request.get_json()
    if not data:
        return jsonify({'status': 'error', 'message': '未收到导出数据'}), 400
    title = data.get('title', '故事')
    results = data.get('results', [])
    doc = Document()
    doc.add_heading(title, 0)
    for idx, item in enumerate(results):
        if item.get('type') == 'text' and item.get('content'):
            doc.add_paragraph(item['content'])
        elif item.get('type') == 'image' and item.get('data'):
            # 处理base64图片
            try:
                import re, base64
                img_data = item['data']
                match = re.match(r'data:(image/\w+);base64,(.+)', img_data)
                if match:
                    img_bytes = base64.b64decode(match.group(2))
                    image_stream = io.BytesIO(img_bytes)
                    doc.add_picture(image_stream, width=Inches(4.5))
            except Exception as e:
                doc.add_paragraph(f'[图片插入失败: {e}]')
        doc.add_paragraph('')  # 空行分隔
    # 保存到内存
    file_stream = io.BytesIO()
    doc.save(file_stream)
    file_stream.seek(0)
    safe_title = ''.join([c for c in title if c.isalnum() or '\u4e00' <= c <= '\u9fa5']) or '故事'
    filename = f"{safe_title}.docx"
    return send_file(file_stream, as_attachment=True, download_name=filename, mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document')

@app.route('/generate_speech_table', methods=['POST'])
def api_generate_speech_table():
    """根据富文本HTML生成配音表格"""
    try:
        data = request.json
        html_text = data.get('html_text', '')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        speech_logger.info(f"生成配音表格请求 - IP: {client_ip}, User-Agent: {user_agent}")
        speech_logger.debug(f"HTML内容长度: {len(html_text)} 字符")

        if not html_text:
            speech_logger.error("未提供HTML文本内容")
            return jsonify({'status': 'error', 'message': 'No html_text provided'})

        table_html = generate_speech_table(html_text)
        speech_logger.info(f"配音表格生成成功 - 表格行数: {table_html.count('<tr>')}")
        speech_logger.debug(f"生成的表格HTML长度: {len(table_html)} 字符")

        return jsonify({
            'status': 'success',
            'table_html': table_html,
            'log_id': str(uuid.uuid4())  # 添加日志ID用于追踪
        })
    except Exception as e:
        speech_logger.error(f"生成配音表格失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/generate_srt_files', methods=['POST'])
def api_generate_srt_files():
    """根据配音表HTML生成SRT文件并返回新表格"""
    try:
        data = request.json
        html_text = data.get('html_text', '')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        log_id = str(uuid.uuid4())

        srt_logger.info(f"生成SRT文件请求 - IP: {client_ip}, User-Agent: {user_agent}, Log-ID: {log_id}")
        srt_logger.debug(f"HTML内容长度: {len(html_text)} 字符")

        if not html_text:
            srt_logger.error("未提供HTML文本内容")
            return jsonify({'status': 'error', 'message': 'No html_text provided'})

        # 记录表格操作
        table_operations = data.get('table_operations', [])
        if table_operations:
            srt_logger.info(f"表格操作记录 - Log-ID: {log_id}")
            for op in table_operations:
                srt_logger.info(f"操作类型: {op.get('type')}, 行号: {op.get('row')}, 列: {op.get('column')}, 值: {op.get('value')}")

        table_html = generate_srt_files_and_table(html_text)
        srt_logger.info(f"SRT文件生成成功 - Log-ID: {log_id}, 表格行数: {table_html.count('<tr>')}")
        srt_logger.debug(f"生成的表格HTML长度: {len(table_html)} 字符")

        return jsonify({
            'status': 'success',
            'table_html': table_html,
            'log_id': log_id
        })
    except Exception as e:
        srt_logger.error(f"生成SRT文件失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/srt_temp/<filename>')
def serve_srt_file(filename):
    """提供srt_temp目录下的SRT文件，纯文本格式"""
    try:
        srt_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'srt_temp')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        srt_logger.info(f"SRT文件下载请求 - 文件: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        if not os.path.exists(os.path.join(srt_dir, filename)):
            srt_logger.error(f"SRT文件不存在: {filename}")
            return "File not found", 404

        srt_logger.info(f"SRT文件下载成功: {filename}")
        return send_from_directory(srt_dir, filename, mimetype='text/plain', as_attachment=False)
    except Exception as e:
        srt_logger.error(f"SRT文件下载失败: {str(e)}", exc_info=True)
        return str(e), 500

@app.route('/preview_voice/<filename>')
def serve_voice_file(filename):
    """提供preview_voice目录下的音频文件"""
    try:
        voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'preview_voice')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        speech_logger.info(f"音频文件预览请求 - 文件: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        if not os.path.exists(os.path.join(voice_dir, filename)):
            speech_logger.error(f"音频文件不存在: {filename}")
            return "File not found", 404

        speech_logger.info(f"音频文件预览成功: {filename}")
        return send_from_directory(voice_dir, filename, mimetype='audio/mpeg')
    except Exception as e:
        speech_logger.error(f"音频文件预览失败: {str(e)}", exc_info=True)
        return str(e), 500

@app.route('/test')
def test():
    return "Server is working!"

@app.route('/audio_test')
def audio_test():
    """音频测试页面，支持传入voice_id和filename参数"""
    voice_id = request.args.get('voice_id', VOICE_OPTIONS[0][0])  # 默认使用第一个声音选项
    filename = request.args.get('filename', '')  # 音频文件名

    # 构建音频文件URL
    audio_url = f'/preview_voice/{filename}' if filename else ''

    # 获取当前声音选项的名称
    voice_name = next((name for id, name in VOICE_OPTIONS if id == voice_id), VOICE_OPTIONS[0][1])

    return render_template('audio_test.html',
                         voice_id=voice_id,
                         voice_name=voice_name,
                         audio_url=audio_url)

@app.route('/templates/<template_name>')
def serve_template(template_name):
    """提供模板HTML文件的静态访问"""
    return send_from_directory('templates', template_name)

@app.route('/save_srt', methods=['POST'])
def save_srt():
    """保存修改后的SRT文件内容"""
    try:
        data = request.json
        filename = data.get('filename')
        content = data.get('content')

        # 安全检查
        if not filename or not content:
            return jsonify({'status': 'error', 'message': '文件名或内容不能为空'})

        # 防止路径遍历
        if '..' in filename or filename.startswith('/'):
            return jsonify({'status': 'error', 'message': '无效的文件名'})

        # 确保只保存到srt_temp目录下的srt文件
        if not filename.endswith('.srt'):
            return jsonify({'status': 'error', 'message': '只能保存SRT文件'})

        srt_path = os.path.join('srt_temp', filename)

        # 记录日志
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        srt_logger.info(f"保存SRT文件 - 文件名: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        # 写入文件
        with open(srt_path, 'w', encoding='utf-8') as f:
            f.write(content)

        srt_logger.info(f"SRT文件保存成功 - 文件名: {filename}, 内容长度: {len(content)}")
        return jsonify({'status': 'success'})

    except Exception as e:
        srt_logger.error(f"保存SRT文件失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/generate_audio', methods=['POST'])
def generate_audio():
    """从SRT文件生成音频文件"""
    try:
        from voice_create import generate_audio_from_srt

        data = request.json
        filename = data.get('filename')
        voice_id = data.get('voice_id')

        # 安全检查
        if not filename or not voice_id:
            return jsonify({'status': 'error', 'message': '文件名或音色ID不能为空'})

        # 防止路径遍历
        if '..' in filename or filename.startswith('/'):
            return jsonify({'status': 'error', 'message': '无效的文件名'})

        # 确保只处理srt_temp目录下的srt文件
        if not filename.endswith('.srt'):
            return jsonify({'status': 'error', 'message': '只能处理SRT文件'})

        srt_path = os.path.join('srt_temp', filename)
        if not os.path.exists(srt_path):
            return jsonify({'status': 'error', 'message': 'SRT文件不存在'})

        # 确保created_voice目录存在
        created_voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "created_voice")
        if not os.path.exists(created_voice_dir):
            os.makedirs(created_voice_dir)
            logging.info(f"Created voice directory: {created_voice_dir}")

        # 记录日志
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        speech_logger.info(f"生成音频请求 - 文件名: {filename}, 音色ID: {voice_id}, IP: {client_ip}, User-Agent: {user_agent}")

        # 确保音色ID是字符串类型
        voice_id_str = str(voice_id)
        speech_logger.info(f"转换后的音色ID: {voice_id_str}")

        # 生成音频文件
        generated_files = generate_audio_from_srt(srt_path, voice_id_str, created_voice_dir)

        if not generated_files:
            return jsonify({'status': 'error', 'message': '音频生成失败'})

        speech_logger.info(f"音频生成成功 - 文件名: {filename}, 生成文件数: {len(generated_files)}")
        return jsonify({
            'status': 'success',
            'files': generated_files,
            'message': f'成功生成{len(generated_files)}个音频文件'
        })

    except Exception as e:
        speech_logger.error(f"生成音频失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/merge_audio', methods=['POST'])
def merge_audio():
    """合并SRT文件对应的所有音频文件"""
    try:
        from audio_merge import merge_audio_files

        data = request.json
        filename = data.get('filename')

        # 安全检查
        if not filename:
            return jsonify({'status': 'error', 'message': '文件名不能为空'})

        # 防止路径遍历
        if '..' in filename or filename.startswith('/'):
            return jsonify({'status': 'error', 'message': '无效的文件名'})

        # 确保只处理srt_temp目录下的srt文件
        if not filename.endswith('.srt'):
            return jsonify({'status': 'error', 'message': '只能处理SRT文件'})

        srt_path = os.path.join('srt_temp', filename)
        if not os.path.exists(srt_path):
            return jsonify({'status': 'error', 'message': 'SRT文件不存在'})

        # 确保created_voice目录存在
        created_voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "created_voice")
        if not os.path.exists(created_voice_dir):
            os.makedirs(created_voice_dir)
            logging.info(f"Created voice directory: {created_voice_dir}")

        # 记录日志
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr
        speech_logger.info(f"合并音频请求 - 文件名: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        # 合并音频文件
        merged_file = merge_audio_files(srt_path, created_voice_dir)

        if not merged_file:
            return jsonify({'status': 'error', 'message': '音频合并失败'})

        speech_logger.info(f"音频合并成功 - 文件名: {filename}, 合并文件: {merged_file}")
        return jsonify({
            'status': 'success',
            'file': merged_file,
            'message': '音频合并成功'
        })

    except Exception as e:
        speech_logger.error(f"合并音频失败: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/created_voice/<filename>')
def serve_created_voice_file(filename):
    """提供created_voice目录下的音频文件"""
    try:
        voice_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'created_voice')
        user_agent = request.headers.get('User-Agent', 'Unknown')
        client_ip = request.remote_addr

        speech_logger.info(f"生成音频文件请求 - 文件: {filename}, IP: {client_ip}, User-Agent: {user_agent}")

        if not os.path.exists(os.path.join(voice_dir, filename)):
            speech_logger.error(f"生成音频文件不存在: {filename}")
            return "File not found", 404

        speech_logger.info(f"生成音频文件访问成功: {filename}")
        return send_from_directory(voice_dir, filename, mimetype='audio/mpeg')
    except Exception as e:
        speech_logger.error(f"生成音频文件访问失败: {str(e)}", exc_info=True)
        return str(e), 500

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "cli":
        # Run in command line mode
        result = generate()
        if result["status"] == "success":
            print(f"Generation successful using key {result['api_key']}")
            for item in result["results"]:
                if item["type"] == "text":
                    print(item["content"])
                else:
                    print(f"[Image generated]")
        else:
            print(f"Generation failed: {result['message']}")
    else:
        # Run as web server
        host = "127.0.0.1"  # 使用本地回环地址
        port = 5000
        print(f"Starting web server on http://{host}:{port}")
        app.run(debug=True, host=host, port=port, threaded=True)


